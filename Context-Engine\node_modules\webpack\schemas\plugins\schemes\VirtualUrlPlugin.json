{"definitions": {"VirtualModule": {"description": "A virtual module definition.", "type": "object", "additionalProperties": false, "properties": {"source": {"description": "The source function that provides the virtual content.", "instanceof": "Function", "tsType": "((loaderContext: import('webpack').LoaderContext<EXPECTED_ANY>) => Promise<string> | string)"}, "type": {"description": "The module type.", "type": "string"}, "version": {"description": "Optional version function or value for cache invalidation.", "anyOf": [{"type": "boolean", "enum": [true]}, {"type": "string"}, {"instanceof": "Function", "tsType": "(() => string | undefined)"}]}}, "required": ["source"]}, "VirtualModuleContent": {"description": "A virtual module can be a string, a function, or a VirtualModule object.", "anyOf": [{"type": "string"}, {"instanceof": "Function", "tsType": "((loaderContext: import('webpack').LoaderContext<EXPECTED_ANY>) => Promise<string> | string)"}, {"$ref": "#/definitions/VirtualModule"}]}, "VirtualUrlOptions": {"description": "Options for building virtual resources.", "type": "object", "additionalProperties": false, "properties": {"modules": {"description": "The virtual modules configuration.", "type": "object", "additionalProperties": {"$ref": "#/definitions/VirtualModuleContent"}}, "scheme": {"description": "The URL scheme to use for virtual resources.", "type": "string"}}, "required": ["modules"]}}, "title": "VirtualUrlPluginOptions", "oneOf": [{"$ref": "#/definitions/VirtualUrlOptions"}]}