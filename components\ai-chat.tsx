"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { useChat } from '@ai-sdk/react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Bot, User, Send, Paperclip, Mic, Square, Copy, ThumbsUp, ThumbsDown, Sparkles, Settings, ArrowDown } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface AIChatProps {
  initialMessages?: Array<{
    id: string
    role: "user" | "assistant"
    content: string
  }>
  systemPrompt?: string
  defaultModel?: string
  defaultProvider?: string
}

export default function AIChat({
  initialMessages = [],
  systemPrompt = "You are a helpful AI assistant.",
  defaultModel = "gpt-4o-mini",
  defaultProvider = "openai"
}: AIChatProps) {
  const [isRecording, setIsRecording] = useState(false)
  const [selectedModel, setSelectedModel] = useState(`${defaultProvider}:${defaultModel}`)
  const [availableModels, setAvailableModels] = useState<Array<{
    provider: string
    model: string
    displayName: string
    value: string
  }>>([])
  const [showScrollButton, setShowScrollButton] = useState(false)
  const [isUserScrolling, setIsUserScrolling] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  // Use the AI SDK's useChat hook
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading,
    error,
    reload,
    stop
  } = useChat({
    api: '/api/chat',
    initialMessages,
    body: {
      provider: selectedModel.split(':')[0],
      model: selectedModel.split(':')[1],
      system: systemPrompt,
    },
    onError: (error) => {
      console.error('Chat error:', error)
    }
  })

  const scrollToBottom = (smooth = true) => {
    messagesEndRef.current?.scrollIntoView({
      behavior: smooth ? "smooth" : "auto"
    })
    setIsUserScrolling(false)
    setShowScrollButton(false)
  }

  // Auto-scroll to bottom when new messages arrive (only if user hasn't scrolled up)
  useEffect(() => {
    if (!isUserScrolling) {
      scrollToBottom(false)
    }
  }, [messages, isUserScrolling])

  // Detect user scrolling
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50 // 50px threshold

    if (!isAtBottom && !isUserScrolling) {
      setIsUserScrolling(true)
      setShowScrollButton(true)
    } else if (isAtBottom && isUserScrolling) {
      setIsUserScrolling(false)
      setShowScrollButton(false)
    }
  }

  // Fetch available models on component mount
  useEffect(() => {
    fetch('/api/models')
      .then(res => res.json())
      .then(data => {
        if (data.models) {
          setAvailableModels(data.models)
        }
      })
      .catch(console.error)
  }, [])

  const copyMessage = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header with Model Selection */}
      <div className="border-b border-[#333] p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              {selectedModel.split(':')[1]}
            </Badge>
            <div className="w-2 h-2 bg-green-400 rounded-full" />
            <span className="text-xs text-gray-400">Online</span>
          </div>
          <div className="flex items-center gap-2">
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-48 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model.value} value={model.value} className="text-xs">
                    {model.displayName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {isLoading && (
              <Button size="sm" variant="outline" onClick={stop}>
                <Square className="w-3 h-3 mr-1" />
                Stop
              </Button>
            )}
          </div>
        </div>
        {error && (
          <div className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded">
            Error: {error.message}
          </div>
        )}
      </div>

      {/* Messages */}
      <div className="flex-1 relative">
        <ScrollArea
          className="h-full"
          ref={scrollAreaRef}
          onScrollCapture={handleScroll}
        >
          <div className="p-4 space-y-4">
          {messages.map((message) => (
          <div key={message.id} className={`flex gap-3 ${message.role === "user" ? "flex-row-reverse" : ""}`}>
            <div className="flex-shrink-0">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  message.role === "user" ? "bg-blue-500" : "bg-gray-600"
                }`}
              >
                {message.role === "user" ? (
                  <User className="w-4 h-4 text-white" />
                ) : (
                  <Bot className="w-4 h-4 text-white" />
                )}
              </div>
            </div>

            <div className={`flex-1 max-w-[80%] ${message.role === "user" ? "text-right" : ""}`}>
              <Card
                className={`${
                  message.role === "user"
                    ? "bg-blue-500 text-white border-blue-500"
                    : "bg-[#1a1a1a] border-[#333] text-gray-100"
                }`}
              >
                <CardContent className="p-3">
                  <div className="whitespace-pre-wrap text-sm leading-relaxed">{message.content}</div>

                  {message.role === "assistant" && (
                    <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-600">
                      <div className="flex items-center gap-2 text-xs text-gray-400">
                        <Badge variant="outline" className="text-xs px-1 py-0">
                          {selectedModel.split(':')[1]}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          size="sm"
                          variant="ghost"
                          className="h-6 w-6 p-0"
                          onClick={() => copyMessage(message.content)}
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <ThumbsUp className="w-3 h-3" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-6 w-6 p-0">
                          <ThumbsDown className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>

              <div className={`text-xs text-gray-500 mt-1 ${message.role === "user" ? "text-right" : ""}`}>
                {new Date(message.createdAt || Date.now()).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
              </div>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-3">
            <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <Card className="bg-[#1a1a1a] border-[#333]">
              <CardContent className="p-3">
                <div className="flex items-center gap-2 text-gray-400">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                    <div
                      className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    />
                    <div
                      className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    />
                  </div>
                  <span className="text-sm">AI is processing...</span>
                </div>
              </CardContent>
            </Card>
          </div>
          )}

            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        {/* Scroll to Bottom Button */}
        {showScrollButton && (
          <div className="absolute bottom-4 right-4">
            <Button
              size="sm"
              variant="outline"
              className="h-8 w-8 p-0 bg-[#1a1a1a] border-[#333] hover:bg-[#2a2a2a] shadow-lg"
              onClick={() => scrollToBottom(true)}
            >
              <ArrowDown className="w-4 h-4 text-blue-400" />
            </Button>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-[#333] p-4">
        <form onSubmit={handleSubmit} className="relative">
          <Input
            value={input}
            onChange={handleInputChange}
            placeholder="Ask AI anything..."
            className="bg-[#1e1e1e] border-[#333] pr-20 focus:border-blue-500 transition-colors"
            disabled={isLoading}
          />
          <div className="absolute right-2 top-1/2 -translate-y-1/2 flex items-center gap-1">
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0" onClick={() => setIsRecording(!isRecording)}>
              {isRecording ? <Square className="w-4 h-4 text-red-400" /> : <Mic className="w-4 h-4 text-gray-400" />}
            </Button>
            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
              <Paperclip className="w-4 h-4 text-gray-400" />
            </Button>
            <Button
              type="submit"
              size="sm"
              className="h-8 w-8 p-0 bg-blue-500 hover:bg-blue-600"
              disabled={!input.trim() || isLoading}
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
