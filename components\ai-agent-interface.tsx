"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Bot, 
  Brain, 
  MessageSquare, 
  Settings, 
  Play, 
  Clock,
  CheckCircle,
  AlertCircle,
  Zap,
  Memory
} from "lucide-react"

interface AgentTask {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  result?: any
  error?: string
  createdAt: string
  updatedAt: string
}

interface AgentMemory {
  conversations: Array<{
    id: string
    messages: Array<{ role: string; content: string }>
    timestamp: string
  }>
  facts: Array<{
    id: string
    content: string
    source: string
    timestamp: string
  }>
  preferences: Record<string, any>
}

export default function AIAgentInterface() {
  const [chatMessage, setChatMessage] = useState("")
  const [taskDescription, setTaskDescription] = useState("")
  const [chatHistory, setChatHistory] = useState<Array<{role: string, content: string}>>([])
  const [tasks, setTasks] = useState<AgentTask[]>([])
  const [memory, setMemory] = useState<AgentMemory | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [agentStatus, setAgentStatus] = useState<'active' | 'inactive'>('inactive')

  // Load agent status and memory on mount
  useEffect(() => {
    loadAgentStatus()
    loadMemory()
  }, [])

  const loadAgentStatus = async () => {
    try {
      const response = await fetch('/api/agent')
      const data = await response.json()
      setAgentStatus(data.status)
    } catch (error) {
      console.error('Failed to load agent status:', error)
    }
  }

  const loadMemory = async () => {
    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'getMemory' })
      })
      const data = await response.json()
      setMemory(data.memory)
    } catch (error) {
      console.error('Failed to load memory:', error)
    }
  }

  const sendChatMessage = async () => {
    if (!chatMessage.trim()) return

    setIsLoading(true)
    const userMessage = { role: 'user', content: chatMessage }
    setChatHistory(prev => [...prev, userMessage])
    setChatMessage("")

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'chat',
          message: chatMessage,
          conversationId: `conv-${Date.now()}`
        })
      })
      
      const data = await response.json()
      const agentMessage = { role: 'assistant', content: data.response }
      setChatHistory(prev => [...prev, agentMessage])
      
      // Refresh memory after chat
      loadMemory()
    } catch (error) {
      console.error('Chat error:', error)
      setChatHistory(prev => [...prev, { 
        role: 'assistant', 
        content: 'Sorry, I encountered an error processing your message.' 
      }])
    } finally {
      setIsLoading(false)
    }
  }

  const executeTask = async () => {
    if (!taskDescription.trim()) return

    setIsLoading(true)
    const newTask: AgentTask = {
      id: `task-${Date.now()}`,
      title: taskDescription,
      description: taskDescription,
      status: 'running',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    setTasks(prev => [...prev, newTask])
    setTaskDescription("")

    try {
      const response = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'executeTask',
          taskDescription
        })
      })
      
      const data = await response.json()
      setTasks(prev => prev.map(task => 
        task.id === newTask.id ? data.task : task
      ))
      
      // Refresh memory after task execution
      loadMemory()
    } catch (error) {
      console.error('Task execution error:', error)
      setTasks(prev => prev.map(task => 
        task.id === newTask.id 
          ? { ...task, status: 'failed', error: 'Execution failed' }
          : task
      ))
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'running': return <Clock className="w-4 h-4 text-blue-400 animate-pulse" />
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-400" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <Card className="bg-[#1a1a1a] border-[#333]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <Brain className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-white text-xl">AI Agent System</CardTitle>
                <p className="text-gray-400 text-sm">
                  Autonomous AI assistant with tool access and memory
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={agentStatus === 'active' ? 'default' : 'secondary'}>
                <Bot className="w-3 h-3 mr-1" />
                {agentStatus}
              </Badge>
              {memory && (
                <Badge variant="outline">
                  <Memory className="w-3 h-3 mr-1" />
                  {memory.facts.length} facts
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Interface */}
      <Tabs defaultValue="chat" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-[#2a2a2a]">
          <TabsTrigger value="chat" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300">
            <MessageSquare className="w-4 h-4 mr-1" />
            Chat
          </TabsTrigger>
          <TabsTrigger value="tasks" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300">
            <Zap className="w-4 h-4 mr-1" />
            Tasks
          </TabsTrigger>
          <TabsTrigger value="memory" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300">
            <Memory className="w-4 h-4 mr-1" />
            Memory
          </TabsTrigger>
        </TabsList>

        <TabsContent value="chat" className="space-y-4">
          <Card className="bg-[#1a1a1a] border-[#333]">
            <CardHeader>
              <CardTitle className="text-white">Agent Chat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ScrollArea className="h-96 bg-[#0d1117] rounded-lg p-4">
                <div className="space-y-3">
                  {chatHistory.map((msg, index) => (
                    <div key={index} className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : ''}`}>
                      <div className={`max-w-[80%] p-3 rounded-lg ${
                        msg.role === 'user' 
                          ? 'bg-blue-500 text-white' 
                          : 'bg-[#2a2a2a] text-gray-100'
                      }`}>
                        <p className="text-sm whitespace-pre-wrap">{msg.content}</p>
                      </div>
                    </div>
                  ))}
                  {isLoading && (
                    <div className="flex gap-3">
                      <div className="bg-[#2a2a2a] text-gray-100 p-3 rounded-lg">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" />
                          <span className="text-sm">Agent is thinking...</span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </ScrollArea>
              
              <div className="flex gap-2">
                <Input
                  value={chatMessage}
                  onChange={(e) => setChatMessage(e.target.value)}
                  placeholder="Ask the agent anything..."
                  className="bg-[#2a2a2a] border-[#444] text-white"
                  onKeyPress={(e) => e.key === 'Enter' && sendChatMessage()}
                  disabled={isLoading}
                />
                <Button 
                  onClick={sendChatMessage} 
                  disabled={!chatMessage.trim() || isLoading}
                  className="bg-blue-500 hover:bg-blue-600"
                >
                  Send
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <Card className="bg-[#1a1a1a] border-[#333]">
            <CardHeader>
              <CardTitle className="text-white">Task Execution</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Textarea
                  value={taskDescription}
                  onChange={(e) => setTaskDescription(e.target.value)}
                  placeholder="Describe a task for the agent to execute..."
                  className="bg-[#2a2a2a] border-[#444] text-white"
                  rows={3}
                />
                <Button 
                  onClick={executeTask}
                  disabled={!taskDescription.trim() || isLoading}
                  className="bg-green-500 hover:bg-green-600"
                >
                  <Play className="w-4 h-4 mr-1" />
                  Execute
                </Button>
              </div>
              
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {tasks.map((task) => (
                    <Card key={task.id} className="bg-[#2a2a2a] border-[#444]">
                      <CardContent className="p-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              {getStatusIcon(task.status)}
                              <span className="text-white text-sm font-medium">{task.title}</span>
                            </div>
                            {task.result && (
                              <p className="text-gray-300 text-xs mt-2">{task.result}</p>
                            )}
                            {task.error && (
                              <p className="text-red-400 text-xs mt-2">{task.error}</p>
                            )}
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {task.status}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="memory" className="space-y-4">
          <Card className="bg-[#1a1a1a] border-[#333]">
            <CardHeader>
              <CardTitle className="text-white">Agent Memory</CardTitle>
            </CardHeader>
            <CardContent>
              {memory ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div className="bg-[#2a2a2a] p-3 rounded-lg">
                      <div className="text-blue-400 text-2xl font-bold">{memory.conversations.length}</div>
                      <div className="text-gray-400 text-sm">Conversations</div>
                    </div>
                    <div className="bg-[#2a2a2a] p-3 rounded-lg">
                      <div className="text-green-400 text-2xl font-bold">{memory.facts.length}</div>
                      <div className="text-gray-400 text-sm">Facts Learned</div>
                    </div>
                    <div className="bg-[#2a2a2a] p-3 rounded-lg">
                      <div className="text-purple-400 text-2xl font-bold">{Object.keys(memory.preferences).length}</div>
                      <div className="text-gray-400 text-sm">Preferences</div>
                    </div>
                  </div>
                  
                  <ScrollArea className="h-48 bg-[#0d1117] rounded-lg p-3">
                    <div className="space-y-2">
                      {memory.facts.slice(-10).map((fact, index) => (
                        <div key={index} className="text-xs text-gray-300 p-2 bg-[#1a1a1a] rounded">
                          {fact.content}
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              ) : (
                <div className="text-center text-gray-400 py-8">
                  <Memory className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No memory data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
