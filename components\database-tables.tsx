"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  Database, 
  Table, 
  Key, 
  Link, 
  Eye,
  Plus,
  Edit,
  Trash2,
  Search,
  Filter,
  Download
} from "lucide-react"

interface DatabaseTablesProps {
  className?: string
}

interface TableColumn {
  name: string
  type: string
  nullable: boolean
  primaryKey?: boolean
  foreignKey?: string
  defaultValue?: string
}

interface DatabaseTable {
  name: string
  schema: string
  rowCount: number
  columns: TableColumn[]
  indexes: string[]
  relationships: Array<{
    type: 'one-to-many' | 'many-to-one' | 'many-to-many'
    table: string
    column: string
  }>
}

export default function DatabaseTables({ className }: DatabaseTablesProps) {
  const mockTables: DatabaseTable[] = [
    {
      name: "users",
      schema: "public",
      rowCount: 1247,
      columns: [
        { name: "id", type: "uuid", nullable: false, primaryKey: true, defaultValue: "gen_random_uuid()" },
        { name: "email", type: "varchar(255)", nullable: false },
        { name: "password_hash", type: "varchar(255)", nullable: false },
        { name: "first_name", type: "varchar(100)", nullable: true },
        { name: "last_name", type: "varchar(100)", nullable: true },
        { name: "created_at", type: "timestamp", nullable: false, defaultValue: "now()" },
        { name: "updated_at", type: "timestamp", nullable: false, defaultValue: "now()" }
      ],
      indexes: ["idx_users_email", "idx_users_created_at"],
      relationships: [
        { type: "one-to-many", table: "projects", column: "user_id" },
        { type: "one-to-many", table: "sessions", column: "user_id" }
      ]
    },
    {
      name: "projects",
      schema: "public", 
      rowCount: 342,
      columns: [
        { name: "id", type: "uuid", nullable: false, primaryKey: true, defaultValue: "gen_random_uuid()" },
        { name: "user_id", type: "uuid", nullable: false, foreignKey: "users.id" },
        { name: "name", type: "varchar(200)", nullable: false },
        { name: "description", type: "text", nullable: true },
        { name: "status", type: "varchar(50)", nullable: false, defaultValue: "'draft'" },
        { name: "created_at", type: "timestamp", nullable: false, defaultValue: "now()" },
        { name: "updated_at", type: "timestamp", nullable: false, defaultValue: "now()" }
      ],
      indexes: ["idx_projects_user_id", "idx_projects_status"],
      relationships: [
        { type: "many-to-one", table: "users", column: "user_id" },
        { type: "one-to-many", table: "tasks", column: "project_id" }
      ]
    },
    {
      name: "tasks",
      schema: "public",
      rowCount: 2891,
      columns: [
        { name: "id", type: "uuid", nullable: false, primaryKey: true, defaultValue: "gen_random_uuid()" },
        { name: "project_id", type: "uuid", nullable: false, foreignKey: "projects.id" },
        { name: "title", type: "varchar(300)", nullable: false },
        { name: "description", type: "text", nullable: true },
        { name: "status", type: "varchar(50)", nullable: false, defaultValue: "'pending'" },
        { name: "priority", type: "varchar(20)", nullable: false, defaultValue: "'medium'" },
        { name: "assigned_to", type: "uuid", nullable: true, foreignKey: "users.id" },
        { name: "due_date", type: "timestamp", nullable: true },
        { name: "created_at", type: "timestamp", nullable: false, defaultValue: "now()" },
        { name: "completed_at", type: "timestamp", nullable: true }
      ],
      indexes: ["idx_tasks_project_id", "idx_tasks_status", "idx_tasks_assigned_to"],
      relationships: [
        { type: "many-to-one", table: "projects", column: "project_id" },
        { type: "many-to-one", table: "users", column: "assigned_to" }
      ]
    }
  ]

  const getTypeColor = (type: string) => {
    if (type.includes('uuid')) return 'text-purple-400'
    if (type.includes('varchar') || type.includes('text')) return 'text-green-400'
    if (type.includes('timestamp') || type.includes('date')) return 'text-blue-400'
    if (type.includes('int') || type.includes('numeric')) return 'text-yellow-400'
    if (type.includes('boolean')) return 'text-red-400'
    return 'text-gray-400'
  }

  return (
    <div className={`h-full ${className || ''}`}>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-[#1a1a1a]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-500/20 rounded-lg">
                <Database className="w-5 h-5 text-green-400" />
              </div>
              <div>
                <h2 className="text-white text-lg font-semibold">Database Tables</h2>
                <p className="text-gray-400 text-sm">
                  Project database schema and table relationships
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs text-gray-300">
                <Table className="w-3 h-3 mr-1" />
                {mockTables.length} tables
              </Badge>
              <Badge variant="outline" className="text-xs text-gray-300">
                <Database className="w-3 h-3 mr-1" />
                PostgreSQL
              </Badge>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          <Tabs defaultValue="tables" className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#1a1a1a]">
              <TabsTrigger value="tables" className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-300">
                <Table className="w-4 h-4 mr-1" />
                Tables
              </TabsTrigger>
              <TabsTrigger value="relationships" className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-300">
                <Link className="w-4 h-4 mr-1" />
                Relationships
              </TabsTrigger>
              <TabsTrigger value="queries" className="data-[state=active]:bg-[#2a2a2a] data-[state=active]:text-white text-gray-300">
                <Search className="w-4 h-4 mr-1" />
                Queries
              </TabsTrigger>
            </TabsList>

            <TabsContent value="tables" className="mt-6 h-full">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {mockTables.map((table) => (
                    <Card key={table.name} className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <Table className="w-5 h-5 text-green-400" />
                            <div>
                              <CardTitle className="text-white text-base">{table.schema}.{table.name}</CardTitle>
                              <p className="text-gray-400 text-sm">{table.rowCount.toLocaleString()} rows</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                              <Eye className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                              <Edit className="w-3 h-3" />
                            </Button>
                            <Button size="sm" variant="ghost" className="h-6 w-6 p-0 text-gray-400 hover:text-white">
                              <Download className="w-3 h-3" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="space-y-2">
                          <h4 className="text-white text-sm font-medium">Columns</h4>
                          <div className="space-y-1">
                            {table.columns.slice(0, 5).map((column) => (
                              <div key={column.name} className="flex items-center justify-between text-xs">
                                <div className="flex items-center gap-2">
                                  {column.primaryKey && <Key className="w-3 h-3 text-yellow-400" />}
                                  {column.foreignKey && <Link className="w-3 h-3 text-blue-400" />}
                                  <span className="text-gray-300">{column.name}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className={`${getTypeColor(column.type)}`}>{column.type}</span>
                                  {!column.nullable && (
                                    <Badge variant="outline" className="text-xs px-1 py-0 text-red-400 border-red-400">
                                      NOT NULL
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            ))}
                            {table.columns.length > 5 && (
                              <div className="text-xs text-gray-500 pt-1">
                                +{table.columns.length - 5} more columns
                              </div>
                            )}
                          </div>
                          
                          {table.indexes.length > 0 && (
                            <div className="pt-2">
                              <h4 className="text-white text-sm font-medium mb-1">Indexes</h4>
                              <div className="flex flex-wrap gap-1">
                                {table.indexes.map((index) => (
                                  <Badge key={index} variant="outline" className="text-xs text-gray-300">
                                    {index}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="relationships" className="mt-6 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
                    <Link className="w-6 h-6 text-blue-400" />
                  </div>
                  <div>
                    <h3 className="text-white text-base font-medium mb-2">Database Relationships</h3>
                    <p className="text-gray-400 text-sm max-w-md">
                      Interactive visualization of table relationships, foreign keys, and data flow between entities.
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs text-gray-300">
                    Coming Soon
                  </Badge>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="queries" className="mt-6 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto">
                    <Search className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-white text-base font-medium mb-2">Query Builder</h3>
                    <p className="text-gray-400 text-sm max-w-md">
                      Visual query builder and SQL editor for exploring and analyzing your project data.
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs text-gray-300">
                    Coming Soon
                  </Badge>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
