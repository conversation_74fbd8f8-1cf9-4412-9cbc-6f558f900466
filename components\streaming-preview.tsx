"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Loader2, Play, Square, FileText, Zap } from "lucide-react"
import { ScrollArea } from "@/components/ui/scroll-area"

interface StreamingPreviewProps {
  isStreaming: boolean
  streamContent: string[]
  onToggleStream: () => void
}

export default function StreamingPreview({ isStreaming, streamContent, onToggleStream }: StreamingPreviewProps) {
  const [currentLine, setCurrentLine] = useState(0)
  const [displayedContent, setDisplayedContent] = useState<string[]>([])

  useEffect(() => {
    if (isStreaming && currentLine < streamContent.length - 1) {
      const timer = setTimeout(() => {
        setCurrentLine((prev) => prev + 1)
        setDisplayedContent((prev) => [...prev, streamContent[currentLine + 1]])
      }, 150)
      return () => clearTimeout(timer)
    }
  }, [isStreaming, currentLine, streamContent])

  useEffect(() => {
    if (!isStreaming) {
      setCurrentLine(0)
      setDisplayedContent([])
    }
  }, [isStreaming])

  const codeLines = [
    `<div className="flex items-center gap-4">`,
    `  <Button variant="outline" size="sm" onClick={onBack}>`,
    `    <ArrowLeft className="w-4 h-4 mr-2" />`,
    `    Back to Editor`,
    `  </Button>`,
    `  <div>`,
    `    <h1 className="text-3xl font-bold text-gray-900">`,
    `      <span className="font-bold text-white">APB</span>`,
    `      <span className="font-bold text-blue-500">X</span>`,
    `      <div className="w-1 h-1 bg-blue-500 rounded-full"></div>`,
    `      AI Development Ecosystem - Admin`,
    `    </h1>`,
    `    <p className="text-gray-600">System monitoring and agent management</p>`,
    `  </div>`,
    `</div>`,
  ]

  return (
    <div className="h-full bg-[#181818] flex items-center justify-center p-8">
      <Card className="w-full max-w-4xl bg-[#1a1a1a] border-[#2a2a2a] shadow-2xl">
        <CardHeader className="border-b border-[#2a2a2a]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <FileText className="w-5 h-5 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-white text-lg">
                  {isStreaming ? "Generating components/admin-dashboard.tsx" : "Ready to Generate"}
                </CardTitle>
                <p className="text-gray-400 text-sm">
                  {isStreaming ? "AG3NT is working..." : "Ready to start generation"}
                </p>
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-0">
          {isStreaming ? (
            <div className="h-96 overflow-hidden">
              {/* Stream Header */}
              <div className="bg-[#181818] border-b border-[#2a2a2a] px-4 py-3 flex items-center gap-3">
                <Loader2 className="w-4 h-4 animate-spin text-blue-400" />
                <span className="text-blue-400 text-sm font-medium">Streaming live code generation...</span>
                <div className="ml-auto flex items-center gap-2">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-xs text-gray-400">Live</span>
                  </div>
                  <span className="text-xs text-gray-500">
                    {currentLine + 1}/{streamContent.length} lines
                  </span>
                </div>
              </div>

              {/* Code Stream */}
              <ScrollArea className="h-full bg-[#181818]">
                <div className="p-4 font-mono text-sm space-y-1">
                  {codeLines.slice(0, Math.min(currentLine + 1, codeLines.length)).map((line, index) => (
                    <div
                      key={index}
                      className={`flex items-start gap-3 transition-all duration-200 ${
                        index === Math.min(currentLine, codeLines.length - 1)
                          ? "text-blue-400 bg-blue-500/10 rounded px-2 py-1"
                          : "text-gray-300"
                      }`}
                    >
                      <span className="text-gray-600 text-xs w-8 text-right flex-shrink-0 mt-0.5">{index + 1}</span>
                      <span className="flex-1">
                        {line.split(" ").map((word, wordIndex) => (
                          <span
                            key={wordIndex}
                            className={
                              word.includes("className") || word.includes("onClick")
                                ? "text-yellow-400"
                                : word.includes('"')
                                  ? "text-green-400"
                                  : word.includes("<") || word.includes(">")
                                    ? "text-cyan-400"
                                    : ""
                            }
                          >
                            {word}{" "}
                          </span>
                        ))}
                      </span>
                    </div>
                  ))}

                  {/* Typing cursor */}
                  {isStreaming && currentLine < codeLines.length - 1 && (
                    <div className="flex items-center gap-3">
                      <span className="text-gray-600 text-xs w-8 text-right">
                        {Math.min(currentLine + 2, codeLines.length)}
                      </span>
                      <div className="w-2 h-4 bg-blue-400 animate-pulse rounded-sm"></div>
                    </div>
                  )}
                </div>
              </ScrollArea>

              {/* Progress Footer */}
              <div className="bg-[#181818] border-t border-[#2a2a2a] px-4 py-2">
                <div className="flex items-center justify-between text-xs">
                  <div className="flex items-center gap-4">
                    <span className="text-gray-400">Progress:</span>
                    <div className="w-32 bg-[#2a2a2a] rounded-full h-1.5">
                      <div
                        className="bg-blue-400 h-1.5 rounded-full transition-all duration-300"
                        style={{ width: `${((currentLine + 1) / streamContent.length) * 100}%` }}
                      />
                    </div>
                    <span className="text-blue-400">
                      {Math.round(((currentLine + 1) / streamContent.length) * 100)}%
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-gray-400">
                    <Zap className="w-3 h-3" />
                    <span>Real-time generation</span>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Idle State */
            <div className="h-96 flex items-center justify-center">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
                  <Play className="w-8 h-8 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-white text-lg font-medium mb-2">Ready to Generate Code</h3>
                  <p className="text-gray-400 text-sm max-w-md">
                    Click "Start Generation" to watch the AI agent write code in real-time. You'll see each line being
                    created with syntax highlighting and live progress tracking.
                  </p>
                </div>
                <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                    <span>Live streaming</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                    <span>Syntax highlighting</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                    <span>Progress tracking</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
