import { openai } from '@ai-sdk/openai'
import { anthropic } from '@ai-sdk/anthropic'
import { google } from '@ai-sdk/google'

// AI Provider Configuration
export const aiProviders = {
  openai: {
    provider: openai,
    models: {
      'gpt-4o': openai('gpt-4o'),
      'gpt-4o-mini': openai('gpt-4o-mini'),
      'gpt-4-turbo': openai('gpt-4-turbo'),
      'gpt-3.5-turbo': openai('gpt-3.5-turbo'),
    }
  },
  openrouter: {
    provider: openai,
    models: {
      'kimi-k2': openai('moonshotai/kimi-k2', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
      'claude-3-5-sonnet': openai('anthropic/claude-3.5-sonnet', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
      'gpt-4o': openai('openai/gpt-4o', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
      'gpt-4o-mini': openai('openai/gpt-4o-mini', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
      'llama-3.1-405b': openai('meta-llama/llama-3.1-405b-instruct', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
      'llama-3.1-70b': openai('meta-llama/llama-3.1-70b-instruct', {
        baseURL: 'https://openrouter.ai/api/v1',
        apiKey: process.env.OPENROUTER_API_KEY,
      }),
    }
  },
  anthropic: {
    provider: anthropic,
    models: {
      'claude-3-5-sonnet': anthropic('claude-3-5-sonnet-20241022'),
      'claude-3-5-haiku': anthropic('claude-3-5-haiku-20241022'),
      'claude-3-opus': anthropic('claude-3-opus-20240229'),
    }
  },
  google: {
    provider: google,
    models: {
      'gemini-1.5-pro': google('gemini-1.5-pro'),
      'gemini-1.5-flash': google('gemini-1.5-flash'),
      'gemini-pro': google('gemini-pro'),
    }
  }
}

// Default configuration
export const defaultConfig = {
  provider: process.env.DEFAULT_AI_PROVIDER || 'openrouter',
  model: process.env.DEFAULT_AI_MODEL || 'kimi-k2',
  maxTokens: parseInt(process.env.MAX_TOKENS_PER_REQUEST || '4000'),
  maxRequestsPerMinute: parseInt(process.env.MAX_REQUESTS_PER_MINUTE || '10'),
}

// Get AI model instance
export function getAIModel(provider: string = defaultConfig.provider, modelName: string = defaultConfig.model) {
  const providerConfig = aiProviders[provider as keyof typeof aiProviders]
  if (!providerConfig) {
    throw new Error(`Unsupported AI provider: ${provider}`)
  }

  const model = providerConfig.models[modelName as keyof typeof providerConfig.models]
  if (!model) {
    throw new Error(`Unsupported model: ${modelName} for provider: ${provider}`)
  }

  return model
}

// Available models for UI selection
export const availableModels = Object.entries(aiProviders).flatMap(([providerName, config]) =>
  Object.keys(config.models).map(modelName => ({
    provider: providerName,
    model: modelName,
    displayName: `${providerName.charAt(0).toUpperCase() + providerName.slice(1)} - ${modelName}`,
    value: `${providerName}:${modelName}`
  }))
)

// Validate environment variables
export function validateEnvironment() {
  const requiredVars = ['OPENROUTER_API_KEY']
  const missingVars = requiredVars.filter(varName => !process.env[varName])
  
  if (missingVars.length > 0) {
    console.warn(`Missing environment variables: ${missingVars.join(', ')}`)
    console.warn('Some AI features may not work properly.')
  }
  
  return missingVars.length === 0
}
