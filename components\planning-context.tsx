"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  FileText, 
  Target, 
  Layers, 
  Users, 
  Clock,
  CheckCircle,
  AlertCircle,
  Brain
} from "lucide-react"

interface PlanningContextProps {
  className?: string
}

export default function PlanningContext({ className }: PlanningContextProps) {
  const mockRequirements = [
    {
      id: "req-1",
      title: "User Authentication System",
      description: "Implement secure user login and registration with JWT tokens",
      status: "completed",
      priority: "high",
      agent: "Design Agent",
      timestamp: "2024-01-15 10:30"
    },
    {
      id: "req-2", 
      title: "Real-time Chat Interface",
      description: "Build WebSocket-based chat with message history and typing indicators",
      status: "in-progress",
      priority: "medium",
      agent: "Planning Agent",
      timestamp: "2024-01-15 11:45"
    },
    {
      id: "req-3",
      title: "Dashboard Analytics",
      description: "Create interactive charts and metrics visualization",
      status: "pending",
      priority: "low",
      agent: "Design Agent", 
      timestamp: "2024-01-15 12:15"
    }
  ]

  const mockDesignDecisions = [
    {
      id: "dd-1",
      title: "Component Architecture",
      decision: "Use React Server Components with Next.js App Router",
      rationale: "Better performance and SEO, simplified data fetching",
      impact: "High",
      agent: "Architecture Agent"
    },
    {
      id: "dd-2",
      title: "State Management",
      decision: "Zustand for client state, React Query for server state",
      rationale: "Lightweight, TypeScript-first, excellent DX",
      impact: "Medium",
      agent: "Design Agent"
    },
    {
      id: "dd-3",
      title: "Styling Strategy",
      decision: "Tailwind CSS with shadcn/ui components",
      rationale: "Consistent design system, rapid development",
      impact: "Medium",
      agent: "Design Agent"
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-400" />
      case 'in-progress': return <Clock className="w-4 h-4 text-blue-400 animate-pulse" />
      case 'pending': return <AlertCircle className="w-4 h-4 text-yellow-400" />
      default: return <Clock className="w-4 h-4 text-gray-400" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  return (
    <div className={`h-full ${className || ''}`}>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-[#1a1a1a]">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-500/20 rounded-lg">
              <Brain className="w-5 h-5 text-blue-400" />
            </div>
            <div>
              <h2 className="text-white text-lg font-semibold">Planning Context</h2>
              <p className="text-gray-400 text-sm">
                Context and decisions created by planning and design agents
              </p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          <Tabs defaultValue="requirements" className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#1a1a1a]">
              <TabsTrigger value="requirements" className="data-[state=active]:bg-[#2a2a2a]">
                <Target className="w-4 h-4 mr-1" />
                Requirements
              </TabsTrigger>
              <TabsTrigger value="decisions" className="data-[state=active]:bg-[#2a2a2a]">
                <Layers className="w-4 h-4 mr-1" />
                Design Decisions
              </TabsTrigger>
              <TabsTrigger value="agents" className="data-[state=active]:bg-[#2a2a2a]">
                <Users className="w-4 h-4 mr-1" />
                Agent Activity
              </TabsTrigger>
            </TabsList>

            <TabsContent value="requirements" className="mt-6 h-full">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {mockRequirements.map((req) => (
                    <Card key={req.id} className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            {getStatusIcon(req.status)}
                            <div className="flex-1">
                              <CardTitle className="text-white text-base">{req.title}</CardTitle>
                              <p className="text-gray-400 text-sm mt-1">{req.description}</p>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${getPriorityColor(req.priority)}`}></div>
                            <Badge variant="outline" className="text-xs">
                              {req.priority}
                            </Badge>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Created by {req.agent}</span>
                          <span>{req.timestamp}</span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="decisions" className="mt-6 h-full">
              <ScrollArea className="h-full">
                <div className="space-y-4">
                  {mockDesignDecisions.map((decision) => (
                    <Card key={decision.id} className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <CardTitle className="text-white text-base">{decision.title}</CardTitle>
                            <p className="text-blue-400 text-sm mt-1 font-medium">{decision.decision}</p>
                            <p className="text-gray-400 text-sm mt-2">{decision.rationale}</p>
                          </div>
                          <Badge variant="outline" className="text-xs">
                            {decision.impact} Impact
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>Decided by {decision.agent}</span>
                          <div className="flex items-center gap-1">
                            <Layers className="w-3 h-3" />
                            <span>Architecture</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="agents" className="mt-6 h-full">
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4">
                  <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto">
                    <Users className="w-6 h-6 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-white text-base font-medium mb-2">Agent Activity Timeline</h3>
                    <p className="text-gray-400 text-sm max-w-md">
                      Real-time activity feed showing agent interactions, decisions, and context creation.
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Coming Soon
                  </Badge>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
