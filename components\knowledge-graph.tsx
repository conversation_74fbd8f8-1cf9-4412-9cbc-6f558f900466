"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { 
  Network, 
  Database, 
  GitBranch, 
  Clock, 
  Zap,
  Eye,
  Settings,
  Play,
  Pause,
  RotateCcw
} from "lucide-react"

interface KnowledgeGraphProps {
  className?: string
}

export default function KnowledgeGraph({ className }: KnowledgeGraphProps) {
  const mockGraphStats = {
    nodes: 1247,
    relationships: 3891,
    lastUpdate: "2 minutes ago",
    activeQueries: 3
  }

  const mockNodeTypes = [
    { type: "Components", count: 342, color: "bg-blue-500" },
    { type: "Functions", count: 589, color: "bg-green-500" },
    { type: "Dependencies", count: 156, color: "bg-purple-500" },
    { type: "Files", count: 89, color: "bg-yellow-500" },
    { type: "Concepts", count: 71, color: "bg-red-500" }
  ]

  const mockRecentQueries = [
    {
      id: "q1",
      query: "Find all components that use useState hook",
      results: 23,
      timestamp: "1 min ago"
    },
    {
      id: "q2", 
      query: "Show dependencies for authentication module",
      results: 8,
      timestamp: "3 min ago"
    },
    {
      id: "q3",
      query: "Trace data flow from API to UI components",
      results: 15,
      timestamp: "5 min ago"
    }
  ]

  return (
    <div className={`h-full ${className || ''}`}>
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="p-6 border-b border-[#1a1a1a]">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-cyan-500/20 rounded-lg">
                <Network className="w-5 h-5 text-cyan-400" />
              </div>
              <div>
                <h2 className="text-white text-lg font-semibold">Knowledge Graph</h2>
                <p className="text-gray-400 text-sm">
                  Temporal codebase relationships via Graphiti/Neo4j
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">
                <Database className="w-3 h-3 mr-1" />
                {mockGraphStats.nodes} nodes
              </Badge>
              <Badge variant="outline" className="text-xs">
                <GitBranch className="w-3 h-3 mr-1" />
                {mockGraphStats.relationships} edges
              </Badge>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 p-6">
          <Tabs defaultValue="visualization" className="h-full">
            <TabsList className="grid w-full grid-cols-3 bg-[#1a1a1a]">
              <TabsTrigger value="visualization" className="data-[state=active]:bg-[#2a2a2a]">
                <Eye className="w-4 h-4 mr-1" />
                Visualization
              </TabsTrigger>
              <TabsTrigger value="analytics" className="data-[state=active]:bg-[#2a2a2a]">
                <Zap className="w-4 h-4 mr-1" />
                Analytics
              </TabsTrigger>
              <TabsTrigger value="queries" className="data-[state=active]:bg-[#2a2a2a]">
                <Database className="w-4 h-4 mr-1" />
                Queries
              </TabsTrigger>
            </TabsList>

            <TabsContent value="visualization" className="mt-6 h-full">
              <div className="h-full flex flex-col">
                {/* Graph Controls */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-2">
                    <Button size="sm" variant="outline" className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <Play className="w-3 h-3 mr-1" />
                      Start Layout
                    </Button>
                    <Button size="sm" variant="outline" className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <Pause className="w-3 h-3 mr-1" />
                      Pause
                    </Button>
                    <Button size="sm" variant="outline" className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <RotateCcw className="w-3 h-3 mr-1" />
                      Reset View
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      <Clock className="w-3 h-3 mr-1" />
                      Updated {mockGraphStats.lastUpdate}
                    </Badge>
                    <Button size="sm" variant="outline" className="bg-[#1a1a1a] border-[#2a2a2a]">
                      <Settings className="w-3 h-3" />
                    </Button>
                  </div>
                </div>

                {/* Graph Visualization Area */}
                <Card className="flex-1 bg-[#0d1117] border-[#2a2a2a]">
                  <CardContent className="h-full p-0">
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center space-y-4">
                        <div className="w-16 h-16 bg-cyan-500/20 rounded-full flex items-center justify-center mx-auto">
                          <Network className="w-8 h-8 text-cyan-400" />
                        </div>
                        <div>
                          <h3 className="text-white text-lg font-medium mb-2">Interactive Graph Visualization</h3>
                          <p className="text-gray-400 text-sm max-w-md">
                            3D force-directed graph showing codebase relationships, dependencies, and temporal connections.
                            Powered by Graphiti and Neo4j for real-time knowledge graph exploration.
                          </p>
                        </div>
                        <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-cyan-400 rounded-full"></div>
                            <span>Force Layout</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span>Clustering</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                            <span>Temporal View</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="analytics" className="mt-6 h-full">
              <div className="space-y-6">
                {/* Stats Overview */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                    <CardContent className="p-4">
                      <div className="text-cyan-400 text-2xl font-bold">{mockGraphStats.nodes}</div>
                      <div className="text-gray-400 text-sm">Total Nodes</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                    <CardContent className="p-4">
                      <div className="text-green-400 text-2xl font-bold">{mockGraphStats.relationships}</div>
                      <div className="text-gray-400 text-sm">Relationships</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                    <CardContent className="p-4">
                      <div className="text-purple-400 text-2xl font-bold">{mockGraphStats.activeQueries}</div>
                      <div className="text-gray-400 text-sm">Active Queries</div>
                    </CardContent>
                  </Card>
                  <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                    <CardContent className="p-4">
                      <div className="text-yellow-400 text-2xl font-bold">98%</div>
                      <div className="text-gray-400 text-sm">Coverage</div>
                    </CardContent>
                  </Card>
                </div>

                {/* Node Types */}
                <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                  <CardHeader>
                    <CardTitle className="text-white text-base">Node Distribution</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockNodeTypes.map((nodeType, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <div className={`w-3 h-3 rounded-full ${nodeType.color}`}></div>
                            <span className="text-gray-300 text-sm">{nodeType.type}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <span className="text-white text-sm font-medium">{nodeType.count}</span>
                            <div className="w-20 bg-[#2a2a2a] rounded-full h-2">
                              <div 
                                className={`h-2 rounded-full ${nodeType.color}`}
                                style={{ width: `${(nodeType.count / 589) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="queries" className="mt-6 h-full">
              <div className="space-y-4">
                <Card className="bg-[#1a1a1a] border-[#2a2a2a]">
                  <CardHeader>
                    <CardTitle className="text-white text-base">Recent Queries</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {mockRecentQueries.map((query) => (
                        <div key={query.id} className="flex items-center justify-between p-3 bg-[#0d1117] rounded-lg">
                          <div className="flex-1">
                            <p className="text-gray-300 text-sm">{query.query}</p>
                            <div className="flex items-center gap-2 mt-1">
                              <Badge variant="outline" className="text-xs">
                                {query.results} results
                              </Badge>
                              <span className="text-gray-500 text-xs">{query.timestamp}</span>
                            </div>
                          </div>
                          <Button size="sm" variant="ghost" className="text-blue-400 hover:text-blue-300">
                            <Eye className="w-3 h-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <div className="flex items-center justify-center py-8">
                  <div className="text-center space-y-4">
                    <div className="w-12 h-12 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto">
                      <Database className="w-6 h-6 text-blue-400" />
                    </div>
                    <div>
                      <h3 className="text-white text-base font-medium mb-2">Cypher Query Interface</h3>
                      <p className="text-gray-400 text-sm max-w-md">
                        Advanced Neo4j Cypher query interface for custom graph exploration and analysis.
                      </p>
                    </div>
                    <Badge variant="outline" className="text-xs">
                      Coming Soon
                    </Badge>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
