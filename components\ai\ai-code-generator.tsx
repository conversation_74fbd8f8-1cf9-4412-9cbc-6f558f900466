"use client"

import { useState, useEffect } from "react"
import { useChat } from '@ai-sdk/react'
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Code, Play, Square, Copy, Download, Sparkles, Zap, CheckCircle, FileText, Settings, Wand2 } from "lucide-react"

interface CodeFile {
  name: string
  content: string
  language: string
}

interface AICodeGeneratorProps {
  onGenerate?: (prompt: string, language: string) => void
  isGenerating?: boolean
  generatedCode?: string
}

export default function AICodeGenerator({
  onGenerate,
  isGenerating = false,
  generatedCode = "",
}: AICodeGeneratorProps) {
  const [prompt, setPrompt] = useState("")
  const [language, setLanguage] = useState("typescript")
  const [selectedModel, setSelectedModel] = useState("openai:gpt-4o-mini")
  const [generatedFiles, setGeneratedFiles] = useState<CodeFile[]>([])
  const [activeTab, setActiveTab] = useState("input")
  const [availableModels, setAvailableModels] = useState<Array<{
    provider: string
    model: string
    displayName: string
    value: string
  }>>([])

  // AI Chat for code generation
  const {
    messages,
    input,
    handleInputChange,
    handleSubmit,
    isLoading: isChatLoading,
    error: chatError,
    setMessages,
  } = useChat({
    api: '/api/chat',
    body: {
      provider: selectedModel.split(':')[0],
      model: selectedModel.split(':')[1],
      system: `You are an expert code generator. Generate clean, well-documented, production-ready code based on user requirements.

      When generating code:
      1. Include proper imports and dependencies
      2. Add TypeScript types when applicable
      3. Include error handling
      4. Add helpful comments
      5. Follow best practices for the chosen language/framework
      6. If multiple files are needed, clearly separate them with file names

      Format your response with clear file separations like:

      // filename.ext
      [code content]

      // another-file.ext
      [more code content]`,
    },
    onFinish: (message) => {
      // Extract code files from the response
      const files = extractCodeFiles(message.content, language)
      setGeneratedFiles(files)
      if (files.length > 0) {
        setActiveTab("output")
      }
    }
  })

  // Fetch available models
  useEffect(() => {
    fetch('/api/models')
      .then(res => res.json())
      .then(data => {
        if (data.models) {
          setAvailableModels(data.models)
        }
      })
      .catch(console.error)
  }, [])

  // Extract code files from AI response
  const extractCodeFiles = (content: string, defaultLanguage: string): CodeFile[] => {
    const files: CodeFile[] = []
    const lines = content.split('\n')
    let currentFile: CodeFile | null = null
    let currentContent: string[] = []

    for (const line of lines) {
      // Check for file separator comments
      const fileMatch = line.match(/^\/\/\s*(.+\.(tsx?|jsx?|py|java|go|rs|cpp|c|h|css|html|json|md))/)
      if (fileMatch) {
        // Save previous file if exists
        if (currentFile) {
          currentFile.content = currentContent.join('\n').trim()
          files.push(currentFile)
        }

        // Start new file
        const fileName = fileMatch[1]
        const extension = fileName.split('.').pop() || defaultLanguage
        currentFile = {
          name: fileName,
          content: '',
          language: getLanguageFromExtension(extension)
        }
        currentContent = []
      } else if (currentFile) {
        currentContent.push(line)
      } else {
        // If no file separator found, treat as single file
        if (files.length === 0) {
          currentFile = {
            name: `generated.${getFileExtension(defaultLanguage)}`,
            content: '',
            language: defaultLanguage
          }
          currentContent = []
        }
        currentContent.push(line)
      }
    }

    // Save last file
    if (currentFile) {
      currentFile.content = currentContent.join('\n').trim()
      files.push(currentFile)
    }

    // If no files were extracted, create a single file with all content
    if (files.length === 0 && content.trim()) {
      files.push({
        name: `generated.${getFileExtension(defaultLanguage)}`,
        content: content.trim(),
        language: defaultLanguage
      })
    }

    return files
  }

  const getLanguageFromExtension = (ext: string): string => {
    const map: Record<string, string> = {
      'ts': 'typescript',
      'tsx': 'typescript',
      'js': 'javascript',
      'jsx': 'javascript',
      'py': 'python',
      'java': 'java',
      'go': 'go',
      'rs': 'rust',
      'cpp': 'cpp',
      'c': 'c',
      'h': 'c',
      'css': 'css',
      'html': 'html',
      'json': 'json',
      'md': 'markdown'
    }
    return map[ext] || ext
  }

  const getFileExtension = (language: string): string => {
    const map: Record<string, string> = {
      'typescript': 'tsx',
      'javascript': 'jsx',
      'python': 'py',
      'java': 'java',
      'go': 'go',
      'rust': 'rs',
      'cpp': 'cpp',
      'c': 'c'
    }
    return map[language] || language
  }

  const handleGenerate = () => {
    if (prompt.trim()) {
      const codePrompt = `Generate ${language} code for the following requirement:

${prompt}

Please provide clean, well-documented, production-ready code. If multiple files are needed, separate them clearly with file names as comments.`

      setMessages([])
      handleSubmit(new Event('submit') as any, {
        data: { message: codePrompt }
      })
    }
  }

  const copyCode = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  const downloadFile = (file: CodeFile) => {
    const element = document.createElement("a")
    const blob = new Blob([file.content], { type: "text/plain" })
    element.href = URL.createObjectURL(blob)
    element.download = file.name
    document.body.appendChild(element)
    element.click()
    document.body.removeChild(element)
  }

  const downloadAllFiles = () => {
    generatedFiles.forEach(file => downloadFile(file))
  }

  return (
    <div className="space-y-4">
      <Card className="bg-[#1a1a1a] border-[#333]">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-white flex items-center gap-2">
              <Wand2 className="w-5 h-5 text-blue-400" />
              AI Code Generator
            </CardTitle>
            <Select value={selectedModel} onValueChange={setSelectedModel}>
              <SelectTrigger className="w-48 h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {availableModels.map((model) => (
                  <SelectItem key={model.value} value={model.value} className="text-xs">
                    {model.displayName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2 bg-[#2a2a2a]">
              <TabsTrigger value="input" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300">
                <Settings className="w-4 h-4 mr-1" />
                Input
              </TabsTrigger>
              <TabsTrigger value="output" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300">
                <Code className="w-4 h-4 mr-1" />
                Output
              </TabsTrigger>
            </TabsList>

            <TabsContent value="input" className="space-y-4 mt-4">
              <div>
                <label className="text-sm font-medium text-gray-300 mb-2 block">
                  Describe what you want to build
                </label>
                <Textarea
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  placeholder="e.g., Create a React todo app with add, delete, and toggle functionality using TypeScript and Tailwind CSS..."
                  className="bg-[#2a2a2a] border-[#444] text-white min-h-[120px] focus:border-blue-500"
                />
              </div>

              <div className="flex gap-4">
                <div className="flex-1">
                  <label className="text-sm font-medium text-gray-300 mb-2 block">Language/Framework</label>
                  <Select value={language} onValueChange={setLanguage}>
                    <SelectTrigger className="bg-[#2a2a2a] border-[#444] text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-[#2a2a2a] border-[#444]">
                      <SelectItem value="typescript">TypeScript/React</SelectItem>
                      <SelectItem value="javascript">JavaScript/React</SelectItem>
                      <SelectItem value="python">Python</SelectItem>
                      <SelectItem value="java">Java</SelectItem>
                      <SelectItem value="go">Go</SelectItem>
                      <SelectItem value="rust">Rust</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleGenerate}
                    disabled={!prompt.trim() || isChatLoading}
                    className="bg-blue-500 hover:bg-blue-600"
                  >
                    {isChatLoading ? (
                      <>
                        <Square className="w-4 h-4 mr-2" />
                        Stop
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        Generate
                      </>
                    )}
                  </Button>
                </div>
              </div>

              {chatError && (
                <div className="text-xs text-red-400 bg-red-900/20 p-2 rounded">
                  Error: {chatError.message}
                </div>
              )}
            </TabsContent>

            <TabsContent value="output" className="space-y-4 mt-4">
              {isChatLoading && (
                <div className="flex items-center justify-center py-8">
                  <div className="flex items-center gap-2 text-blue-400">
                    <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />
                    <span>AI is generating your code...</span>
                  </div>
                </div>
              )}

              {generatedFiles.length > 0 && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge className="bg-green-500">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        {generatedFiles.length} file{generatedFiles.length > 1 ? 's' : ''} generated
                      </Badge>
                    </div>
                    <Button size="sm" variant="outline" onClick={downloadAllFiles}>
                      <Download className="w-4 h-4 mr-1" />
                      Download All
                    </Button>
                  </div>

                  <Tabs defaultValue={generatedFiles[0]?.name} className="w-full">
                    <TabsList className="bg-[#2a2a2a] flex-wrap h-auto p-1">
                      {generatedFiles.map((file) => (
                        <TabsTrigger
                          key={file.name}
                          value={file.name}
                          className="data-[state=active]:bg-blue-500 data-[state=active]:text-white text-gray-300 text-xs"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          {file.name}
                        </TabsTrigger>
                      ))}
                    </TabsList>

                    {generatedFiles.map((file) => (
                      <TabsContent key={file.name} value={file.name} className="mt-4">
                        <Card className="bg-[#0d1117] border-[#333]">
                          <CardHeader className="pb-2">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <Code className="w-4 h-4 text-green-400" />
                                <span className="text-sm font-medium text-white">{file.name}</span>
                                <Badge variant="outline" className="text-xs">
                                  {file.language}
                                </Badge>
                              </div>
                              <div className="flex items-center gap-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => copyCode(file.content)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Copy className="w-3 h-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={() => downloadFile(file)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Download className="w-3 h-3" />
                                </Button>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="bg-[#0d1117] rounded-lg p-4 font-mono text-sm overflow-auto max-h-96 border border-[#333]">
                              <pre className="text-gray-300 whitespace-pre-wrap">
                                {file.content}
                              </pre>
                            </div>
                          </CardContent>
                        </Card>
                      </TabsContent>
                    ))}
                  </Tabs>
                </div>
              )}

              {!isChatLoading && generatedFiles.length === 0 && (
                <div className="flex items-center justify-center py-8 text-gray-400">
                  <div className="text-center">
                    <Code className="w-8 h-8 mx-auto mb-2 opacity-50" />
                    <p>No code generated yet. Go to the Input tab to create your first generation.</p>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
